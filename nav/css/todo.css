/* TodoList 样式文件 - 与导航页主题系统集成 */

/* ===== 基础容器样式 ===== */
.todo-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: var(--card-background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.todo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.todo-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.todo-title .todo-icon {
    font-size: 1.2em;
}

.todo-stats {
    display: flex;
    gap: 16px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.todo-stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* ===== 输入区域样式 ===== */
.todo-input-section {
    margin-bottom: 24px;
}

.todo-input-form {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.todo-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.todo-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--accent-color-alpha);
}

.todo-input::placeholder {
    color: var(--text-muted);
}

.todo-add-btn {
    padding: 12px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 6px;
}

.todo-add-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.todo-add-btn:active {
    transform: translateY(0);
}

/* ===== 筛选区域样式 ===== */
.todo-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: var(--surface-color);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.todo-filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.todo-filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.todo-tag-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.todo-tag-chip {
    padding: 4px 12px;
    background: var(--surface-hover);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
}

.todo-tag-chip:hover {
    background: var(--accent-color-alpha);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.todo-tag-chip.active {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.todo-clear-filters {
    padding: 6px 12px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-muted);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.todo-clear-filters:hover {
    background: var(--surface-hover);
    color: var(--text-secondary);
}

/* ===== 任务列表样式 ===== */
.todo-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.todo-item {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 16px;
    transition: all var(--transition-fast);
    position: relative;
}

.todo-item:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-sm);
}

.todo-item.completed {
    opacity: 0.7;
}

.todo-item.completed .todo-text {
    text-decoration: line-through;
    color: var(--text-muted);
}

.todo-main {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.todo-checkbox {
    width: 18px;
    height: 18px;
    margin-top: 2px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.todo-content {
    flex: 1;
    min-width: 0;
}

.todo-text {
    font-size: 0.875rem;
    color: var(--text-primary);
    line-height: 1.5;
    margin: 0 0 8px 0;
    word-wrap: break-word;
}

.todo-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    margin-bottom: 8px;
}

.todo-countdown {
    font-size: 0.75rem;
    color: var(--warning-color);
    background: rgba(245, 158, 11, 0.1);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.todo-countdown.expired {
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.1);
}

.todo-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.todo-tag {
    font-size: 0.75rem;
    color: var(--accent-color);
    background: var(--accent-color-alpha);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    border: 1px solid transparent;
}

.todo-progress {
    font-size: 0.75rem;
    color: var(--text-muted);
    background: var(--surface-color);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
}

.todo-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.todo-item:hover .todo-actions {
    opacity: 1;
}

.todo-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    font-size: 0.75rem;
}

.todo-action-btn:hover {
    background: var(--surface-hover);
    color: var(--text-secondary);
}

.todo-action-btn.danger:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.todo-action-btn.primary:hover {
    background: var(--accent-color-alpha);
    color: var(--accent-color);
}

/* ===== 子步骤样式 ===== */
.todo-substeps {
    margin-top: 12px;
    padding-left: 30px;
    border-left: 2px solid var(--border-color);
}

.todo-substeps.collapsed {
    display: none;
}

.todo-substep {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.todo-substep:last-child {
    border-bottom: none;
}

.todo-substep-checkbox {
    width: 16px;
    height: 16px;
    margin-top: 1px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.todo-substep-text {
    flex: 1;
    font-size: 0.8125rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.todo-substep.completed .todo-substep-text {
    text-decoration: line-through;
    color: var(--text-muted);
}

.todo-substep-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.todo-substep:hover .todo-substep-actions {
    opacity: 1;
}

.todo-substep-add {
    margin-top: 8px;
    display: flex;
    gap: 8px;
}

.todo-substep-input {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.8125rem;
}

.todo-substep-add-btn {
    padding: 6px 12px;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.todo-substep-add-btn:hover {
    background: var(--accent-hover);
}

/* ===== 展开/折叠按钮 ===== */
.todo-toggle-substeps {
    display: flex;
    align-items: center;
    gap: 4px;
    background: transparent;
    border: none;
    color: var(--text-muted);
    font-size: 0.75rem;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.todo-toggle-substeps:hover {
    background: var(--surface-hover);
    color: var(--text-secondary);
}

.todo-toggle-substeps .toggle-icon {
    transition: transform var(--transition-fast);
}

.todo-toggle-substeps.expanded .toggle-icon {
    transform: rotate(90deg);
}

/* ===== 备忘录样式 ===== */
.todo-memo {
    margin-top: 8px;
    padding: 8px 12px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 0.8125rem;
    color: var(--text-secondary);
    line-height: 1.4;
    white-space: pre-wrap;
}

.todo-memo-input {
    width: 100%;
    min-height: 60px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.8125rem;
    font-family: inherit;
    resize: vertical;
    transition: all var(--transition-fast);
}

.todo-memo-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

/* ===== 日期时间选择器样式 ===== */
.todo-datetime-input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.8125rem;
    transition: all var(--transition-fast);
}

.todo-datetime-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

/* ===== 模态框样式 ===== */
.todo-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.todo-modal.show {
    opacity: 1;
    visibility: visible;
}

.todo-modal-content {
    background: var(--card-background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.todo-modal.show .todo-modal-content {
    transform: scale(1);
}

.todo-modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.todo-modal-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.todo-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.todo-modal-close:hover {
    background: var(--surface-hover);
    color: var(--text-secondary);
}

.todo-modal-body {
    padding: 20px 24px;
}

.todo-modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* ===== 表单样式 ===== */
.todo-form-group {
    margin-bottom: 16px;
}

.todo-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 6px;
}

.todo-form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--surface-color);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.todo-form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

.todo-form-textarea {
    min-height: 80px;
    resize: vertical;
    font-family: inherit;
}

/* ===== 按钮样式 ===== */
.todo-btn {
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    user-select: none;
}

.todo-btn-primary {
    background: var(--primary-color);
    color: white;
}

.todo-btn-primary:hover {
    background: var(--primary-hover);
}

.todo-btn-secondary {
    background: var(--surface-hover);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.todo-btn-secondary:hover {
    background: var(--surface-color);
    color: var(--text-primary);
}

.todo-btn-danger {
    background: var(--error-color);
    color: white;
}

.todo-btn-danger:hover {
    background: #dc2626;
}

.todo-btn-sm {
    padding: 6px 12px;
    font-size: 0.8125rem;
}

/* ===== 空状态样式 ===== */
.todo-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.todo-empty-state .empty-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.todo-empty-state .empty-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin: 0 0 8px 0;
}

.todo-empty-state .empty-description {
    font-size: 0.875rem;
    margin: 0;
}

/* ===== 归档视图样式 ===== */
.todo-archived-container {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.todo-archived-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.todo-archived-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.todo-archived-toggle {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 6px 12px;
    border-radius: var(--radius-sm);
    font-size: 0.8125rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.todo-archived-toggle:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .todo-container {
        margin: 0;
        padding: 16px;
        border-radius: 0;
    }

    .todo-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .todo-stats {
        width: 100%;
        justify-content: space-between;
    }

    .todo-input-form {
        flex-direction: column;
    }

    .todo-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .todo-main {
        flex-direction: column;
        gap: 8px;
    }

    .todo-actions {
        opacity: 1;
        margin-left: 0;
        margin-top: 8px;
    }

    .todo-substeps {
        padding-left: 16px;
    }

    .todo-modal-content {
        width: 95%;
        margin: 20px;
    }

    .todo-modal-header,
    .todo-modal-body,
    .todo-modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
}

@media (max-width: 480px) {
    .todo-container {
        padding: 12px;
    }

    .todo-title {
        font-size: 1.25rem;
    }

    .todo-stats {
        flex-direction: column;
        gap: 8px;
    }

    .todo-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .todo-actions {
        flex-wrap: wrap;
    }
}

/* ===== 动画效果 ===== */
@keyframes todoSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes todoSlideOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

.todo-item.entering {
    animation: todoSlideIn 0.3s ease-out;
}

.todo-item.leaving {
    animation: todoSlideOut 0.3s ease-out;
}

/* ===== 主题特定样式调整 ===== */
[data-theme="dark-obsidian"] .todo-container {
    background: var(--surface-color);
}

[data-theme="dark-obsidian"] .todo-item {
    background: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="jasmine-green"] .todo-tag {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

[data-theme="navy-blue"] .todo-countdown {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

[data-theme="star-moonlight-blue"] .todo-container {
    backdrop-filter: blur(10px);
}

/* ===== 确认对话框样式 ===== */
.todo-confirm-dialog {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 8px 0;
}

/* 确认对话框的快速动画 */
.todo-modal.todo-confirm-modal {
    transition: all var(--transition-fast);
}

.todo-modal.todo-confirm-modal .todo-modal-content {
    transition: transform var(--transition-fast);
}

.todo-confirm-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: var(--warning-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.todo-confirm-content {
    flex: 1;
    min-width: 0;
}

.todo-confirm-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.todo-confirm-message {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

/* 危险按钮样式 */
.todo-btn-danger {
    background: var(--error-color);
    color: white;
    border: 1px solid var(--error-color);
}

.todo-btn-danger:hover {
    background: var(--error-color-dark, #dc2626);
    border-color: var(--error-color-dark, #dc2626);
    transform: translateY(-1px);
}

.todo-btn-danger:active {
    transform: translateY(0);
}
