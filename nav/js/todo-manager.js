/**
 * TodoList 管理器
 * 实现完整的任务管理功能，包括CRUD、子步骤、截止日期、标签、归档等
 */
class TodoManager {
    constructor() {
        this.todos = [];
        this.archivedTodos = [];
        this.tags = new Set();
        this.currentFilter = { tags: [], showCompleted: true };
        this.storageKey = 'fs-navigation-todos';
        this.archivedStorageKey = 'fs-navigation-todos-archived';
        this.tagsStorageKey = 'fs-navigation-todos-tags';
        
        // DOM 元素
        this.container = null;
        this.todoInput = null;
        this.todoList = null;
        this.tagFilter = null;
        this.archivedContainer = null;
        
        // 定时器
        this.countdownTimer = null;
        
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.loadData();
        this.startCountdownTimer();
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'todo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 创建主任务
     */
    createTodo(text, dueDate = null, tags = []) {
        if (!text.trim()) return null;

        const todo = {
            id: this.generateId(),
            text: text.trim(),
            completed: false,
            createdAt: new Date().toISOString(),
            dueDate: dueDate,
            memo: '',
            isSubstepsVisible: false,
            tags: [...tags],
            isArchived: false,
            substeps: []
        };

        this.todos.unshift(todo);
        this.updateTags(tags);
        this.saveData();
        return todo;
    }

    /**
     * 更新任务
     */
    updateTodo(id, updates) {
        const todoIndex = this.todos.findIndex(t => t.id === id);
        if (todoIndex === -1) return false;

        const oldTags = [...this.todos[todoIndex].tags];
        this.todos[todoIndex] = { ...this.todos[todoIndex], ...updates };
        
        // 更新标签集合
        if (updates.tags) {
            this.updateTags(updates.tags);
            this.cleanupUnusedTags();
        }

        this.saveData();
        return true;
    }

    /**
     * 删除任务
     */
    deleteTodo(id) {
        const todoIndex = this.todos.findIndex(t => t.id === id);
        if (todoIndex === -1) return false;

        this.todos.splice(todoIndex, 1);
        this.cleanupUnusedTags();
        this.saveData();
        return true;
    }

    /**
     * 切换任务完成状态
     */
    toggleTodo(id) {
        const todo = this.todos.find(t => t.id === id);
        if (!todo) return false;

        todo.completed = !todo.completed;
        this.saveData();
        return true;
    }

    /**
     * 归档任务
     */
    archiveTodo(id) {
        const todoIndex = this.todos.findIndex(t => t.id === id);
        if (todoIndex === -1) return false;

        const todo = this.todos[todoIndex];
        if (!todo.completed) return false; // 只能归档已完成的任务

        todo.isArchived = true;
        todo.archivedAt = new Date().toISOString();
        
        this.archivedTodos.unshift(todo);
        this.todos.splice(todoIndex, 1);
        
        this.saveData();
        return true;
    }

    /**
     * 恢复归档任务
     */
    restoreTodo(id) {
        const archivedIndex = this.archivedTodos.findIndex(t => t.id === id);
        if (archivedIndex === -1) return false;

        const todo = this.archivedTodos[archivedIndex];
        todo.isArchived = false;
        delete todo.archivedAt;
        
        this.todos.unshift(todo);
        this.archivedTodos.splice(archivedIndex, 1);
        
        this.saveData();
        return true;
    }

    /**
     * 永久删除归档任务
     */
    permanentDeleteTodo(id) {
        const archivedIndex = this.archivedTodos.findIndex(t => t.id === id);
        if (archivedIndex === -1) return false;

        this.archivedTodos.splice(archivedIndex, 1);
        this.saveData();
        return true;
    }

    /**
     * 添加子步骤
     */
    addSubstep(todoId, text) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo || !text.trim()) return null;

        const substep = {
            id: this.generateId(),
            text: text.trim(),
            completed: false,
            memo: ''
        };

        todo.substeps.push(substep);
        this.saveData();
        return substep;
    }

    /**
     * 更新子步骤
     */
    updateSubstep(todoId, substepId, updates) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo) return false;

        const substepIndex = todo.substeps.findIndex(s => s.id === substepId);
        if (substepIndex === -1) return false;

        todo.substeps[substepIndex] = { ...todo.substeps[substepIndex], ...updates };
        this.saveData();
        return true;
    }

    /**
     * 删除子步骤
     */
    deleteSubstep(todoId, substepId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo) return false;

        const substepIndex = todo.substeps.findIndex(s => s.id === substepId);
        if (substepIndex === -1) return false;

        todo.substeps.splice(substepIndex, 1);
        this.saveData();
        return true;
    }

    /**
     * 切换子步骤完成状态
     */
    toggleSubstep(todoId, substepId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo) return false;

        const substep = todo.substeps.find(s => s.id === substepId);
        if (!substep) return false;

        substep.completed = !substep.completed;
        this.saveData();
        return true;
    }

    /**
     * 切换子步骤可见性
     */
    toggleSubstepsVisibility(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo) return false;

        todo.isSubstepsVisible = !todo.isSubstepsVisible;
        this.saveData();
        return true;
    }

    /**
     * 更新标签集合
     */
    updateTags(newTags) {
        newTags.forEach(tag => {
            if (tag.trim()) {
                this.tags.add(tag.trim());
            }
        });
        this.saveTags();
    }

    /**
     * 清理未使用的标签
     */
    cleanupUnusedTags() {
        const usedTags = new Set();
        this.todos.forEach(todo => {
            todo.tags.forEach(tag => usedTags.add(tag));
        });
        this.archivedTodos.forEach(todo => {
            todo.tags.forEach(tag => usedTags.add(tag));
        });
        
        this.tags = usedTags;
        this.saveTags();
    }

    /**
     * 获取过滤后的任务列表
     */
    getFilteredTodos() {
        let filtered = [...this.todos];

        // 标签筛选
        if (this.currentFilter.tags.length > 0) {
            filtered = filtered.filter(todo => 
                this.currentFilter.tags.some(tag => todo.tags.includes(tag))
            );
        }

        // 完成状态筛选
        if (!this.currentFilter.showCompleted) {
            filtered = filtered.filter(todo => !todo.completed);
        }

        return filtered;
    }

    /**
     * 设置筛选条件
     */
    setFilter(filter) {
        this.currentFilter = { ...this.currentFilter, ...filter };
    }

    /**
     * 清除筛选条件
     */
    clearFilter() {
        this.currentFilter = { tags: [], showCompleted: true };
    }

    /**
     * 计算倒计时
     */
    calculateCountdown(dueDate) {
        if (!dueDate) return null;

        const now = new Date();
        const due = new Date(dueDate);
        const diff = due.getTime() - now.getTime();

        if (diff <= 0) {
            return { expired: true, text: '已过期' };
        }

        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        let text = '';
        if (days > 0) text += `${days}天`;
        if (hours > 0) text += `${hours}小时`;
        if (minutes > 0) text += `${minutes}分钟`;

        return { expired: false, text: text || '即将到期' };
    }

    /**
     * 启动倒计时定时器
     */
    startCountdownTimer() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }

        this.countdownTimer = setInterval(() => {
            this.updateCountdowns();
        }, 60000); // 每分钟更新一次
    }

    /**
     * 更新所有倒计时显示
     */
    updateCountdowns() {
        if (this.container) {
            const countdownElements = this.container.querySelectorAll('.todo-countdown');
            countdownElements.forEach(element => {
                const todoId = element.closest('.todo-item').dataset.todoId;
                const todo = this.todos.find(t => t.id === todoId);
                if (todo && todo.dueDate) {
                    const countdown = this.calculateCountdown(todo.dueDate);
                    if (countdown) {
                        element.textContent = countdown.text;
                        element.classList.toggle('expired', countdown.expired);
                    }
                }
            });
        }
    }

    /**
     * 保存数据到 localStorage
     */
    saveData() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.todos));
            localStorage.setItem(this.archivedStorageKey, JSON.stringify(this.archivedTodos));
        } catch (error) {
            console.error('保存 TodoList 数据失败:', error);
        }
    }

    /**
     * 从 localStorage 加载数据
     */
    loadData() {
        try {
            const todosData = localStorage.getItem(this.storageKey);
            const archivedData = localStorage.getItem(this.archivedStorageKey);
            const tagsData = localStorage.getItem(this.tagsStorageKey);

            if (todosData) {
                this.todos = JSON.parse(todosData);
            }

            if (archivedData) {
                this.archivedTodos = JSON.parse(archivedData);
            }

            if (tagsData) {
                this.tags = new Set(JSON.parse(tagsData));
            } else {
                // 从现有任务中提取标签
                this.extractTagsFromTodos();
            }
        } catch (error) {
            console.error('加载 TodoList 数据失败:', error);
            this.todos = [];
            this.archivedTodos = [];
            this.tags = new Set();
        }
    }

    /**
     * 保存标签到 localStorage
     */
    saveTags() {
        try {
            localStorage.setItem(this.tagsStorageKey, JSON.stringify([...this.tags]));
        } catch (error) {
            console.error('保存标签数据失败:', error);
        }
    }

    /**
     * 从现有任务中提取标签
     */
    extractTagsFromTodos() {
        const allTags = new Set();
        [...this.todos, ...this.archivedTodos].forEach(todo => {
            todo.tags.forEach(tag => allTags.add(tag));
        });
        this.tags = allTags;
        this.saveTags();
    }

    /**
     * 导出数据
     */
    exportData() {
        const data = {
            todos: this.todos,
            archivedTodos: this.archivedTodos,
            tags: [...this.tags],
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `todolist-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
    }

    /**
     * 导入数据
     */
    async importData(file, mode = 'merge') {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);

                    // 验证数据格式
                    if (!this.validateImportData(data)) {
                        reject(new Error('无效的数据格式'));
                        return;
                    }

                    if (mode === 'overwrite') {
                        // 覆盖模式：完全替换现有数据
                        this.todos = data.todos || [];
                        this.archivedTodos = data.archivedTodos || [];
                        this.tags = new Set(data.tags || []);
                    } else {
                        // 合并模式：根据ID合并数据
                        this.mergeImportData(data);
                    }

                    this.saveData();
                    resolve(data);
                } catch (error) {
                    reject(new Error('文件解析失败: ' + error.message));
                }
            };

            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    /**
     * 验证导入数据格式
     */
    validateImportData(data) {
        if (!data || typeof data !== 'object') return false;

        // 检查必要字段
        if (!Array.isArray(data.todos)) return false;

        // 验证任务数据结构
        for (const todo of data.todos) {
            if (!todo.id || !todo.text || typeof todo.completed !== 'boolean') {
                return false;
            }
        }

        return true;
    }

    /**
     * 合并导入数据
     */
    mergeImportData(data) {
        const existingIds = new Set(this.todos.map(t => t.id));
        const existingArchivedIds = new Set(this.archivedTodos.map(t => t.id));

        // 合并主任务
        if (data.todos) {
            data.todos.forEach(todo => {
                if (existingIds.has(todo.id)) {
                    // 更新现有任务
                    const index = this.todos.findIndex(t => t.id === todo.id);
                    this.todos[index] = { ...this.todos[index], ...todo };
                } else {
                    // 添加新任务
                    this.todos.push(todo);
                }
            });
        }

        // 合并归档任务
        if (data.archivedTodos) {
            data.archivedTodos.forEach(todo => {
                if (existingArchivedIds.has(todo.id)) {
                    // 更新现有归档任务
                    const index = this.archivedTodos.findIndex(t => t.id === todo.id);
                    this.archivedTodos[index] = { ...this.archivedTodos[index], ...todo };
                } else {
                    // 添加新归档任务
                    this.archivedTodos.push(todo);
                }
            });
        }

        // 合并标签
        if (data.tags) {
            data.tags.forEach(tag => this.tags.add(tag));
        }
    }

    /**
     * 清空所有任务
     */
    clearAllTodos() {
        this.todos = [];
        this.archivedTodos = [];
        this.tags = new Set();
        this.saveData();
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const totalTodos = this.todos.length;
        const completedTodos = this.todos.filter(t => t.completed).length;
        const pendingTodos = totalTodos - completedTodos;
        const archivedTodos = this.archivedTodos.length;
        const totalTags = this.tags.size;

        // 过期任务统计
        const expiredTodos = this.todos.filter(todo => {
            if (!todo.dueDate || todo.completed) return false;
            const countdown = this.calculateCountdown(todo.dueDate);
            return countdown && countdown.expired;
        }).length;

        return {
            totalTodos,
            completedTodos,
            pendingTodos,
            archivedTodos,
            totalTags,
            expiredTodos
        };
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
    }
}
