/**
 * TodoList UI 组件
 * 负责渲染和交互逻辑
 */
class TodoUI {
    constructor(todoManager) {
        this.todoManager = todoManager;
        this.container = null;
        this.isVisible = false;
        this.currentView = 'main'; // 'main' | 'archived'
        
        // 绑定方法上下文
        this.handleAddTodo = this.handleAddTodo.bind(this);
        this.handleKeyPress = this.handleKeyPress.bind(this);
        this.handleFilterChange = this.handleFilterChange.bind(this);
    }

    /**
     * 初始化 UI
     */
    init(container) {
        console.log('TodoUI.init() 开始...');
        this.container = container;

        try {
            this.render();
            this.bindEvents();
            console.log('TodoUI.init() 完成');
        } catch (error) {
            console.error('TodoUI.init() 失败:', error);
            if (this.container) {
                this.container.innerHTML = `
                    <div style="color: red; padding: 20px; border: 1px solid red; border-radius: 8px;">
                        <h3>TodoList 初始化失败</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    }

    /**
     * 渲染主界面
     */
    render() {
        if (!this.container) return;

        const stats = this.todoManager.getStats();
        
        this.container.innerHTML = `
            <div class="todo-container">
                <div class="todo-header">
                    <h2 class="todo-title">
                        <i class="fas fa-tasks todo-icon"></i>
                        待办清单
                    </h2>
                    <div class="todo-stats">
                        <div class="todo-stat-item">
                            <i class="fas fa-list"></i>
                            <span>${stats.totalTodos} 个任务</span>
                        </div>
                        <div class="todo-stat-item">
                            <i class="fas fa-check-circle"></i>
                            <span>${stats.completedTodos} 已完成</span>
                        </div>
                        <div class="todo-stat-item">
                            <i class="fas fa-clock"></i>
                            <span>${stats.pendingTodos} 待处理</span>
                        </div>
                        ${stats.expiredTodos > 0 ? `
                        <div class="todo-stat-item">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span style="color: var(--error-color)">${stats.expiredTodos} 已过期</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                <div class="todo-input-section">
                    <form class="todo-input-form" id="todoInputForm">
                        <input type="text" 
                               class="todo-input" 
                               id="todoInput" 
                               placeholder="添加新任务..." 
                               maxlength="200">
                        <button type="submit" class="todo-add-btn">
                            <i class="fas fa-plus"></i>
                            添加
                        </button>
                    </form>
                    
                    ${this.renderFilters()}
                </div>

                <div class="todo-list" id="todoList">
                    ${this.renderTodoList()}
                </div>

                ${this.renderToolbar()}
            </div>
        `;

        this.updateStats();
    }

    /**
     * 渲染筛选器
     */
    renderFilters() {
        const tags = [...this.todoManager.tags];
        const currentFilter = this.todoManager.currentFilter;

        if (tags.length === 0) return '';

        return `
            <div class="todo-filters">
                <div class="todo-filter-group">
                    <span class="todo-filter-label">标签筛选:</span>
                    <div class="todo-tag-filter">
                        ${tags.map(tag => `
                            <span class="todo-tag-chip ${currentFilter.tags.includes(tag) ? 'active' : ''}" 
                                  data-tag="${tag}">
                                ${tag}
                            </span>
                        `).join('')}
                    </div>
                </div>
                <div class="todo-filter-group">
                    <button class="todo-clear-filters" id="clearFilters">
                        <i class="fas fa-times"></i>
                        清除筛选
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染任务列表
     */
    renderTodoList() {
        const todos = this.todoManager.getFilteredTodos();
        
        if (todos.length === 0) {
            return this.renderEmptyState();
        }

        return todos.map(todo => this.renderTodoItem(todo)).join('');
    }

    /**
     * 渲染单个任务项
     */
    renderTodoItem(todo) {
        const countdown = todo.dueDate ? this.todoManager.calculateCountdown(todo.dueDate) : null;
        const substepsCount = todo.substeps.length;
        const completedSubsteps = todo.substeps.filter(s => s.completed).length;
        
        return `
            <div class="todo-item ${todo.completed ? 'completed' : ''}" data-todo-id="${todo.id}">
                <div class="todo-main">
                    <input type="checkbox" 
                           class="todo-checkbox" 
                           ${todo.completed ? 'checked' : ''}
                           data-action="toggle-todo">
                    
                    <div class="todo-content">
                        <p class="todo-text">${this.escapeHtml(todo.text)}</p>
                        
                        <div class="todo-meta">
                            ${countdown ? `
                                <span class="todo-countdown ${countdown.expired ? 'expired' : ''}">
                                    <i class="fas fa-clock"></i>
                                    ${countdown.text}
                                </span>
                            ` : ''}
                            
                            ${todo.tags.length > 0 ? `
                                <div class="todo-tags">
                                    ${todo.tags.map(tag => `
                                        <span class="todo-tag">${this.escapeHtml(tag)}</span>
                                    `).join('')}
                                </div>
                            ` : ''}
                            
                            ${substepsCount > 0 ? `
                                <span class="todo-progress">
                                    <i class="fas fa-list-ul"></i>
                                    ${completedSubsteps}/${substepsCount}
                                </span>
                            ` : ''}
                        </div>

                        ${todo.memo ? `
                            <div class="todo-memo">${this.escapeHtml(todo.memo)}</div>
                        ` : ''}

                        ${substepsCount > 0 ? `
                            <button class="todo-toggle-substeps ${todo.isSubstepsVisible ? 'expanded' : ''}" 
                                    data-action="toggle-substeps">
                                <i class="fas fa-chevron-right toggle-icon"></i>
                                ${todo.isSubstepsVisible ? '收起' : '展开'}子步骤
                            </button>
                        ` : ''}

                        ${this.renderSubsteps(todo)}
                    </div>

                    <div class="todo-actions">
                        <button class="todo-action-btn primary" 
                                data-action="edit-todo" 
                                title="编辑任务">
                            <i class="fas fa-edit"></i>
                        </button>
                        
                        <button class="todo-action-btn" 
                                data-action="add-substep" 
                                title="添加子步骤">
                            <i class="fas fa-plus"></i>
                        </button>
                        
                        ${todo.completed ? `
                            <button class="todo-action-btn primary" 
                                    data-action="archive-todo" 
                                    title="归档任务">
                                <i class="fas fa-archive"></i>
                            </button>
                        ` : ''}
                        
                        <button class="todo-action-btn danger" 
                                data-action="delete-todo" 
                                title="删除任务">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染子步骤
     */
    renderSubsteps(todo) {
        if (todo.substeps.length === 0) return '';

        return `
            <div class="todo-substeps ${todo.isSubstepsVisible ? '' : 'collapsed'}">
                ${todo.substeps.map(substep => `
                    <div class="todo-substep ${substep.completed ? 'completed' : ''}" 
                         data-substep-id="${substep.id}">
                        <input type="checkbox" 
                               class="todo-substep-checkbox" 
                               ${substep.completed ? 'checked' : ''}
                               data-action="toggle-substep">
                        
                        <span class="todo-substep-text">${this.escapeHtml(substep.text)}</span>
                        
                        <div class="todo-substep-actions">
                            <button class="todo-action-btn" 
                                    data-action="edit-substep" 
                                    title="编辑子步骤">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="todo-action-btn danger" 
                                    data-action="delete-substep" 
                                    title="删除子步骤">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('')}
                
                <div class="todo-substep-add">
                    <input type="text" 
                           class="todo-substep-input" 
                           placeholder="添加子步骤..." 
                           maxlength="100"
                           data-todo-id="${todo.id}">
                    <button class="todo-substep-add-btn" data-action="add-substep-confirm">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        const hasFilters = this.todoManager.currentFilter.tags.length > 0;
        
        return `
            <div class="todo-empty-state">
                <div class="empty-icon">
                    <i class="fas fa-${hasFilters ? 'filter' : 'tasks'}"></i>
                </div>
                <h3 class="empty-title">
                    ${hasFilters ? '没有匹配的任务' : '暂无待办任务'}
                </h3>
                <p class="empty-description">
                    ${hasFilters ? '尝试调整筛选条件或清除筛选' : '点击上方输入框添加你的第一个任务'}
                </p>
            </div>
        `;
    }

    /**
     * 渲染工具栏
     */
    renderToolbar() {
        const archivedCount = this.todoManager.archivedTodos.length;
        
        return `
            <div class="todo-toolbar" style="margin-top: 24px; padding-top: 16px; border-top: 1px solid var(--border-color);">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px;">
                    <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                        ${archivedCount > 0 ? `
                            <button class="todo-btn todo-btn-secondary" data-action="view-archived">
                                <i class="fas fa-archive"></i>
                                已归档 (${archivedCount})
                            </button>
                        ` : ''}
                        
                        <button class="todo-btn todo-btn-secondary" data-action="export-data">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                        
                        <label class="todo-btn todo-btn-secondary" style="cursor: pointer;">
                            <i class="fas fa-upload"></i>
                            导入数据
                            <input type="file" accept=".json" style="display: none;" data-action="import-data">
                        </label>
                    </div>
                    
                    <div style="display: flex; gap: 12px;">
                        <button class="todo-btn todo-btn-danger todo-btn-sm" data-action="clear-all">
                            <i class="fas fa-trash"></i>
                            清空所有
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 移除事件监听器
     */
    unbindEvents() {
        if (!this.container) return;

        // 移除表单提交事件
        const form = this.container.querySelector('#todoInputForm');
        if (form) {
            form.removeEventListener('submit', this.handleAddTodo);
        }

        // 移除键盘事件
        const input = this.container.querySelector('#todoInput');
        if (input) {
            input.removeEventListener('keypress', this.handleKeyPress);
        }

        // 移除委托事件（需要使用相同的绑定函数引用）
        if (this.boundHandlers) {
            this.container.removeEventListener('click', this.boundHandlers.click);
            this.container.removeEventListener('change', this.boundHandlers.change);
            this.container.removeEventListener('keypress', this.boundHandlers.keypress);
            this.container.removeEventListener('click', this.boundHandlers.filterChange);
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (!this.container) return;

        // 先移除旧的事件监听器
        this.unbindEvents();

        // 创建绑定函数引用，用于后续移除
        this.boundHandlers = {
            click: this.handleClick.bind(this),
            change: this.handleChange.bind(this),
            keypress: this.handleSubstepKeyPress.bind(this),
            filterChange: this.handleFilterChange.bind(this)
        };

        // 表单提交
        const form = this.container.querySelector('#todoInputForm');
        if (form) {
            form.addEventListener('submit', this.handleAddTodo);
        }

        // 键盘事件
        const input = this.container.querySelector('#todoInput');
        if (input) {
            input.addEventListener('keypress', this.handleKeyPress);
        }

        // 委托事件处理
        this.container.addEventListener('click', this.boundHandlers.click);
        this.container.addEventListener('change', this.boundHandlers.change);
        this.container.addEventListener('keypress', this.boundHandlers.keypress);

        // 筛选器事件
        this.container.addEventListener('click', this.boundHandlers.filterChange);
    }

    /**
     * 处理添加任务
     */
    handleAddTodo(e) {
        e.preventDefault();
        const input = this.container.querySelector('#todoInput');
        const text = input.value.trim();
        
        if (text) {
            this.todoManager.createTodo(text);
            input.value = '';
            this.refresh();
        }
    }

    /**
     * 处理键盘事件
     */
    handleKeyPress(e) {
        if (e.key === 'Enter') {
            this.handleAddTodo(e);
        }
    }

    /**
     * 处理子步骤键盘事件
     */
    handleSubstepKeyPress(e) {
        if (e.key === 'Enter' && e.target.classList.contains('todo-substep-input')) {
            const todoId = e.target.dataset.todoId;
            const text = e.target.value.trim();
            
            if (text && todoId) {
                this.todoManager.addSubstep(todoId, text);
                e.target.value = '';
                this.refresh();
            }
        }
    }

    /**
     * 处理点击事件
     */
    handleClick(e) {
        const action = e.target.closest('[data-action]')?.dataset.action;
        if (!action) return;

        const todoItem = e.target.closest('.todo-item');
        const substepItem = e.target.closest('.todo-substep');
        const todoId = todoItem?.dataset.todoId;
        const substepId = substepItem?.dataset.substepId;

        switch (action) {
            case 'toggle-todo':
                if (todoId) {
                    this.todoManager.toggleTodo(todoId);
                    this.refresh();
                }
                break;

            case 'toggle-substep':
                if (todoId && substepId) {
                    this.todoManager.toggleSubstep(todoId, substepId);
                    this.refresh();
                }
                break;

            case 'toggle-substeps':
                if (todoId) {
                    this.todoManager.toggleSubstepsVisibility(todoId);
                    this.refresh();
                }
                break;

            case 'delete-todo':
                if (todoId) {
                    this.showConfirmDialog('确定要删除这个任务吗？', '删除后将无法恢复', () => {
                        this.todoManager.deleteTodo(todoId);
                        this.refresh();
                    });
                }
                break;

            case 'delete-substep':
                if (todoId && substepId) {
                    this.showConfirmDialog('确定要删除这个子步骤吗？', '删除后将无法恢复', () => {
                        this.todoManager.deleteSubstep(todoId, substepId);
                        this.refresh();
                    });
                }
                break;

            case 'archive-todo':
                if (todoId) {
                    this.todoManager.archiveTodo(todoId);
                    this.refresh();
                }
                break;

            case 'add-substep-confirm':
                const input = e.target.closest('.todo-substep-add').querySelector('.todo-substep-input');
                const text = input.value.trim();
                if (text && todoId) {
                    this.todoManager.addSubstep(todoId, text);
                    input.value = '';
                    this.refresh();
                }
                break;

            case 'export-data':
                this.todoManager.exportData();
                break;

            case 'clear-all':
                this.showConfirmDialog('确定要清空所有任务吗？', '此操作不可恢复！', () => {
                    this.todoManager.clearAllTodos();
                    this.refresh();
                });
                break;

            case 'edit-todo':
                this.showEditTodoModal(todoId);
                break;

            case 'edit-substep':
                this.showEditSubstepModal(todoId, substepId);
                break;

            case 'view-archived':
                this.showArchivedView();
                break;
        }
    }

    /**
     * 处理变化事件
     */
    handleChange(e) {
        const action = e.target.dataset.action;

        if (action === 'import-data') {
            const file = e.target.files[0];
            if (file) {
                this.handleImportData(file);
            }
        }
    }

    /**
     * 处理筛选变化
     */
    handleFilterChange(e) {
        if (e.target.classList.contains('todo-tag-chip')) {
            const tag = e.target.dataset.tag;
            const currentTags = [...this.todoManager.currentFilter.tags];

            if (currentTags.includes(tag)) {
                // 移除标签
                const index = currentTags.indexOf(tag);
                currentTags.splice(index, 1);
            } else {
                // 添加标签
                currentTags.push(tag);
            }

            this.todoManager.setFilter({ tags: currentTags });
            this.refresh();
        } else if (e.target.id === 'clearFilters') {
            this.todoManager.clearFilter();
            this.refresh();
        }
    }

    /**
     * 处理导入数据
     */
    async handleImportData(file) {
        try {
            const mode = await this.showImportModeDialog();
            if (!mode) return;

            await this.todoManager.importData(file, mode);
            this.refresh();
            this.showMessage('数据导入成功！', 'success');
        } catch (error) {
            this.showMessage('导入失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示导入模式对话框
     */
    showImportModeDialog() {
        return new Promise((resolve) => {
            const modal = this.createModal('选择导入模式', `
                <p style="margin-bottom: 16px; color: var(--text-secondary);">
                    请选择如何处理导入的数据：
                </p>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="radio" name="importMode" value="merge" checked>
                        <div>
                            <strong>合并模式</strong>
                            <div style="font-size: 0.8125rem; color: var(--text-muted);">
                                将导入的任务与现有任务合并，相同ID的任务将被更新
                            </div>
                        </div>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="radio" name="importMode" value="overwrite">
                        <div>
                            <strong style="color: var(--error-color);">覆盖模式</strong>
                            <div style="font-size: 0.8125rem; color: var(--text-muted);">
                                完全替换现有数据（将丢失所有现有任务）
                            </div>
                        </div>
                    </label>
                </div>
            `, [
                {
                    text: '取消',
                    class: 'todo-btn-secondary',
                    click: () => {
                        this.closeModal();
                        resolve(null);
                    }
                },
                {
                    text: '确定导入',
                    class: 'todo-btn-primary',
                    click: () => {
                        const mode = modal.querySelector('input[name="importMode"]:checked')?.value;
                        this.closeModal();
                        resolve(mode);
                    }
                }
            ]);
        });
    }

    /**
     * 显示编辑任务模态框
     */
    showEditTodoModal(todoId) {
        const todo = this.todoManager.todos.find(t => t.id === todoId);
        if (!todo) return;

        const modal = this.createModal('编辑任务', `
            <form id="editTodoForm">
                <div class="todo-form-group">
                    <label class="todo-form-label">任务内容</label>
                    <input type="text" class="todo-form-input" name="text" value="${this.escapeHtml(todo.text)}" required maxlength="200">
                </div>

                <div class="todo-form-group">
                    <label class="todo-form-label">截止日期</label>
                    <input type="datetime-local" class="todo-form-input" name="dueDate" value="${todo.dueDate ? new Date(todo.dueDate).toISOString().slice(0, 16) : ''}">
                </div>

                <div class="todo-form-group">
                    <label class="todo-form-label">标签（用逗号分隔）</label>
                    <input type="text" class="todo-form-input" name="tags" value="${todo.tags.join(', ')}" placeholder="工作, 学习, 生活">
                </div>

                <div class="todo-form-group">
                    <label class="todo-form-label">备忘录</label>
                    <textarea class="todo-form-input todo-form-textarea" name="memo" placeholder="添加备忘录...">${this.escapeHtml(todo.memo)}</textarea>
                </div>
            </form>
        `, [
            {
                text: '取消',
                class: 'todo-btn-secondary',
                click: () => this.closeModal()
            },
            {
                text: '保存',
                class: 'todo-btn-primary',
                click: () => {
                    const form = modal.querySelector('#editTodoForm');
                    const formData = new FormData(form);

                    const updates = {
                        text: formData.get('text').trim(),
                        dueDate: formData.get('dueDate') || null,
                        tags: formData.get('tags').split(',').map(t => t.trim()).filter(t => t),
                        memo: formData.get('memo').trim()
                    };

                    if (updates.text) {
                        this.todoManager.updateTodo(todoId, updates);
                        this.refresh();
                        this.closeModal();
                    }
                }
            }
        ]);
    }

    /**
     * 显示编辑子步骤模态框
     */
    showEditSubstepModal(todoId, substepId) {
        const todo = this.todoManager.todos.find(t => t.id === todoId);
        const substep = todo?.substeps.find(s => s.id === substepId);
        if (!substep) return;

        const modal = this.createModal('编辑子步骤', `
            <form id="editSubstepForm">
                <div class="todo-form-group">
                    <label class="todo-form-label">子步骤内容</label>
                    <input type="text" class="todo-form-input" name="text" value="${this.escapeHtml(substep.text)}" required maxlength="100">
                </div>

                <div class="todo-form-group">
                    <label class="todo-form-label">备忘录</label>
                    <textarea class="todo-form-input todo-form-textarea" name="memo" placeholder="添加备忘录...">${this.escapeHtml(substep.memo)}</textarea>
                </div>
            </form>
        `, [
            {
                text: '取消',
                class: 'todo-btn-secondary',
                click: () => this.closeModal()
            },
            {
                text: '保存',
                class: 'todo-btn-primary',
                click: () => {
                    const form = modal.querySelector('#editSubstepForm');
                    const formData = new FormData(form);

                    const updates = {
                        text: formData.get('text').trim(),
                        memo: formData.get('memo').trim()
                    };

                    if (updates.text) {
                        this.todoManager.updateSubstep(todoId, substepId, updates);
                        this.refresh();
                        this.closeModal();
                    }
                }
            }
        ]);
    }

    /**
     * 显示归档视图
     */
    showArchivedView() {
        const archivedTodos = this.todoManager.archivedTodos;

        const modal = this.createModal('已归档任务', `
            <div style="max-height: 400px; overflow-y: auto;">
                ${archivedTodos.length === 0 ? `
                    <div class="todo-empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-archive"></i>
                        </div>
                        <h3 class="empty-title">暂无归档任务</h3>
                        <p class="empty-description">完成的任务归档后会出现在这里</p>
                    </div>
                ` : archivedTodos.map(todo => `
                    <div class="todo-item completed" data-todo-id="${todo.id}" style="margin-bottom: 12px;">
                        <div class="todo-main">
                            <div class="todo-content">
                                <p class="todo-text">${this.escapeHtml(todo.text)}</p>
                                <div class="todo-meta">
                                    <span style="font-size: 0.75rem; color: var(--text-muted);">
                                        归档于: ${new Date(todo.archivedAt).toLocaleString()}
                                    </span>
                                    ${todo.tags.length > 0 ? `
                                        <div class="todo-tags">
                                            ${todo.tags.map(tag => `
                                                <span class="todo-tag">${this.escapeHtml(tag)}</span>
                                            `).join('')}
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                            <div class="todo-actions" style="opacity: 1;">
                                <button class="todo-action-btn primary"
                                        data-action="restore-todo"
                                        title="恢复任务">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="todo-action-btn danger"
                                        data-action="permanent-delete"
                                        title="永久删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `, [
            {
                text: '关闭',
                class: 'todo-btn-secondary',
                click: () => this.closeModal()
            }
        ]);

        // 绑定归档视图的事件
        modal.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.dataset.action;
            const todoId = e.target.closest('.todo-item')?.dataset.todoId;

            if (action === 'restore-todo' && todoId) {
                this.todoManager.restoreTodo(todoId);
                this.refresh();
                this.closeModal();
            } else if (action === 'permanent-delete' && todoId) {
                this.showConfirmDialog('确定要永久删除这个任务吗？', '此操作不可恢复！', () => {
                    this.todoManager.permanentDeleteTodo(todoId);
                    this.refresh();
                    this.closeModal();
                });
            }
        });
    }

    /**
     * 创建模态框
     */
    createModal(title, content, buttons = []) {
        // 先关闭现有的模态框，避免重复
        this.closeModal();

        const modal = document.createElement('div');
        modal.className = 'todo-modal';
        modal.innerHTML = `
            <div class="todo-modal-content">
                <div class="todo-modal-header">
                    <h3 class="todo-modal-title">${title}</h3>
                    <button class="todo-modal-close" data-action="close-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="todo-modal-body">
                    ${content}
                </div>
                ${buttons.length > 0 ? `
                    <div class="todo-modal-footer">
                        ${buttons.map(btn => `
                            <button class="todo-btn ${btn.class}" data-modal-action="${btn.text}">
                                ${btn.text}
                            </button>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;

        // 使用事件委托绑定按钮事件，避免重复绑定
        buttons.forEach(btn => {
            const button = modal.querySelector(`[data-modal-action="${btn.text}"]`);
            if (button && btn.click) {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    btn.click();
                }, { once: true }); // 使用 once: true 确保只执行一次
            }
        });

        // 绑定关闭事件
        const closeButton = modal.querySelector('[data-action="close-modal"]');
        if (closeButton) {
            closeButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.closeModal();
            }, { once: true });
        }

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                e.preventDefault();
                e.stopPropagation();
                this.closeModal();
            }
        });

        document.body.appendChild(modal);
        setTimeout(() => modal.classList.add('show'), 10);

        return modal;
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        // 查找所有模态框并关闭
        const modals = document.querySelectorAll('.todo-modal');
        modals.forEach(modal => {
            modal.classList.remove('show');
            // 立即移除事件监听器，避免重复触发
            modal.style.pointerEvents = 'none';
            setTimeout(() => {
                if (document.body.contains(modal)) {
                    try {
                        document.body.removeChild(modal);
                    } catch (e) {
                        console.debug('Modal removal failed:', e);
                    }
                }
            }, 300);
        });
    }

    /**
     * 快速关闭模态框（用于确认对话框）
     */
    closeModalFast() {
        // 查找所有模态框并强制关闭
        const modals = document.querySelectorAll('.todo-modal');
        modals.forEach(modal => {
            // 立即隐藏模态框
            modal.style.opacity = '0';
            modal.style.visibility = 'hidden';
            modal.style.pointerEvents = 'none';
            modal.classList.remove('show');

            // 快速移除DOM元素
            setTimeout(() => {
                if (document.body.contains(modal)) {
                    try {
                        document.body.removeChild(modal);
                    } catch (e) {
                        // 如果移除失败，忽略错误
                        console.debug('Modal removal failed:', e);
                    }
                }
            }, 50); // 更快的移除时间
        });
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 简单的消息提示实现
        const messageEl = document.createElement('div');
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? 'var(--success-color)' : type === 'error' ? 'var(--error-color)' : 'var(--info-color)'};
            color: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            z-index: 1001;
            font-size: 0.875rem;
            max-width: 300px;
            word-wrap: break-word;
        `;
        messageEl.textContent = message;

        document.body.appendChild(messageEl);

        setTimeout(() => {
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(messageEl)) {
                    document.body.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const stats = this.todoManager.getStats();
        const statsContainer = this.container?.querySelector('.todo-stats');

        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="todo-stat-item">
                    <i class="fas fa-list"></i>
                    <span>${stats.totalTodos} 个任务</span>
                </div>
                <div class="todo-stat-item">
                    <i class="fas fa-check-circle"></i>
                    <span>${stats.completedTodos} 已完成</span>
                </div>
                <div class="todo-stat-item">
                    <i class="fas fa-clock"></i>
                    <span>${stats.pendingTodos} 待处理</span>
                </div>
                ${stats.expiredTodos > 0 ? `
                <div class="todo-stat-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span style="color: var(--error-color)">${stats.expiredTodos} 已过期</span>
                </div>
                ` : ''}
            `;
        }
    }

    /**
     * 刷新界面
     */
    refresh() {
        this.render();
        this.bindEvents();
    }

    /**
     * 显示/隐藏 TodoList
     */
    toggle() {
        this.isVisible = !this.isVisible;
        if (this.container) {
            this.container.style.display = this.isVisible ? 'block' : 'none';
        }
    }

    /**
     * 显示 TodoList
     */
    show() {
        this.isVisible = true;
        if (this.container) {
            this.container.style.display = 'block';
        }
    }

    /**
     * 隐藏 TodoList
     */
    hide() {
        this.isVisible = false;
        if (this.container) {
            this.container.style.display = 'none';
        }
    }

    /**
     * HTML 转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(title, message, onConfirm) {
        const modal = this.createModal('确认操作', `
            <div class="todo-confirm-dialog">
                <div class="todo-confirm-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="todo-confirm-content">
                    <h4 class="todo-confirm-title">${this.escapeHtml(title)}</h4>
                    <p class="todo-confirm-message">${this.escapeHtml(message)}</p>
                </div>
            </div>
        `, [
            {
                text: '取消',
                class: 'todo-btn-secondary',
                click: () => {
                    this.closeModalFast();
                }
            },
            {
                text: '确定',
                class: 'todo-btn-danger',
                click: () => {
                    // 先关闭模态框，避免refresh()干扰
                    this.closeModalFast();
                    // 然后执行确认操作
                    if (onConfirm) {
                        // 稍微延迟执行，确保模态框开始关闭动画
                        setTimeout(() => onConfirm(), 50);
                    }
                }
            }
        ]);

        // 为确认对话框添加特殊的CSS类以实现更快的动画
        modal.classList.add('todo-confirm-modal');

        return modal;
    }

    /**
     * 销毁 UI
     */
    destroy() {
        // 移除事件监听器
        this.unbindEvents();

        if (this.container) {
            this.container.innerHTML = '';
        }
        this.closeModal();
    }
}
