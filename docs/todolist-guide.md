# TodoList 功能使用指南

## 概述

FaciShare 导航页面现已集成了完整的 TodoList（待办清单）功能，提供了一个功能丰富的任务管理工具。该功能位于侧边栏的"待办清单"选项中，在"常用"分类之上。

## 主要功能

### 1. 基础任务管理
- **创建任务**: 在输入框中输入任务内容，点击"添加"按钮或按回车键
- **完成任务**: 点击任务左侧的复选框标记任务为已完成
- **编辑任务**: 点击任务右侧的编辑按钮修改任务内容
- **删除任务**: 点击任务右侧的删除按钮移除任务

### 2. 高级功能

#### 截止日期与倒计时
- 为任务设置精确到分钟的截止日期
- 实时显示倒计时（格式：X天X小时X分钟）
- 过期任务会显示醒目的"已过期"提示

#### 子步骤管理
- 为主任务添加多个子步骤
- 每个子步骤有独立的完成状态
- 显示子步骤完成进度（如：2/5）
- 支持展开/折叠子步骤列表

#### 任务标签
- 为任务添加多个标签进行分类
- 支持标签筛选功能
- 自动补全已有标签

#### 备忘录
- 为主任务和子步骤添加详细备忘录
- 支持多行文本内容

#### 任务归档
- 已完成的任务可以归档
- 归档任务不会显示在主列表中
- 可以查看、恢复或永久删除归档任务

### 3. 数据管理

#### 数据持久化
- 所有数据自动保存到浏览器本地存储
- 刷新页面后数据完整保留

#### 导入导出
- **导出**: 将所有任务数据导出为 JSON 文件
- **导入**: 支持两种导入模式
  - **合并模式**: 将导入任务与现有任务合并
  - **覆盖模式**: 完全替换现有数据（需二次确认）

#### 数据清理
- 支持一键清空所有任务（需二次确认）
- 自动清理未使用的标签

### 4. 筛选功能
- 按标签筛选任务
- 显示/隐藏已完成任务
- 一键清除所有筛选条件

## 界面说明

### 主界面布局
1. **顶部统计**: 显示任务总数、已完成数、待处理数、过期数
2. **输入区域**: 快速添加新任务
3. **筛选区域**: 标签筛选和筛选控制
4. **任务列表**: 显示所有任务项
5. **工具栏**: 归档查看、数据管理等功能

### 任务项信息
- **复选框**: 标记完成状态
- **任务文本**: 主要内容
- **倒计时**: 截止日期倒计时（如有）
- **标签**: 彩色标签显示
- **进度**: 子步骤完成进度
- **操作按钮**: 编辑、添加子步骤、归档、删除

## 主题集成

TodoList 完全集成了导航页面的主题系统，支持所有主题变体：
- **日光象牙白**: 温暖舒适的浅色主题
- **夜月玄玉黑**: 深邃优雅的深色主题
- **清雅茉莉绿**: 清新自然的绿色主题
- **深邃海军蓝**: 沉稳专业的蓝色主题
- **星月流光蓝**: 浩瀚星空的流光主题

## 响应式设计

TodoList 采用响应式设计，在不同设备上都有良好的体验：
- **桌面端**: 完整功能和最佳布局
- **平板端**: 适配中等屏幕尺寸
- **手机端**: 优化的移动端交互

## 使用技巧

### 1. 高效任务管理
- 使用标签对任务进行分类（如：工作、学习、生活）
- 为重要任务设置截止日期
- 将复杂任务分解为多个子步骤
- 定期归档已完成的任务保持列表整洁

### 2. 数据安全
- 定期导出数据进行备份
- 在重要操作前先导出当前数据
- 使用合并模式导入避免数据丢失

### 3. 筛选使用
- 使用标签筛选专注于特定类型的任务
- 在不同场景下隐藏已完成任务减少干扰
- 利用搜索功能快速定位特定任务

## 快捷键

- **回车键**: 在输入框中快速添加任务
- **回车键**: 在子步骤输入框中快速添加子步骤

## 注意事项

1. **数据存储**: 数据存储在浏览器本地，清除浏览器数据会丢失任务
2. **备份重要**: 建议定期导出数据进行备份
3. **浏览器兼容**: 支持现代浏览器（Chrome、Firefox、Safari、Edge）
4. **性能优化**: 建议任务总数不超过 200 个以保证最佳性能

## 故障排除

### 常见问题
1. **任务不显示**: 检查是否有筛选条件，尝试清除筛选
2. **数据丢失**: 检查浏览器是否清除了本地存储
3. **功能异常**: 尝试刷新页面重新加载

### 数据恢复
如果数据意外丢失，可以尝试：
1. 检查是否有之前导出的备份文件
2. 使用导入功能恢复数据
3. 检查浏览器的本地存储是否被清除

## 更新日志

### v1.0 (2025-08-18)
- 初始版本发布
- 完整的任务管理功能
- 主题系统集成
- 响应式设计
- 数据导入导出
- 任务归档功能
